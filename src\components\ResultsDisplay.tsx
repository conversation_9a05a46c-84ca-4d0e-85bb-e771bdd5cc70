'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveC<PERSON>r, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';
import { Table, Download, Eye, Code, TrendingUp, Calendar } from 'lucide-react';

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
}

interface ResultsDisplayProps {
  queryHistory: QueryResult[];
  onQuerySelect: (result: QueryResult) => void;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export default function ResultsDisplay({ queryHistory, onQuerySelect }: ResultsDisplayProps) {
  const [selectedResult, setSelectedResult] = useState<QueryResult | null>(
    queryHistory.length > 0 ? queryHistory[0] : null
  );
  const [viewMode, setViewMode] = useState<'table' | 'chart' | 'sql'>('table');

  // دالة لترجمة أسماء الأعمدة إلى العربية
  const translateColumnName = (columnName: string): string => {
    const translations: { [key: string]: string } = {
      // أسماء الأعمدة الأساسية
      'ItemName': 'اسم المنتج',
      'ClientName': 'اسم العميل',
      'BranchName': 'اسم الفرع',
      'CategoryName': 'اسم الفئة',
      'DocumentName': 'نوع الوثيقة',
      'TheDate': 'التاريخ',
      'Quantity': 'الكمية',
      'Amount': 'المبلغ',
      'UnitPrice': 'سعر الوحدة',

      // أسماء الأعمدة المحسوبة
      'TotalQuantity': 'إجمالي الكمية',
      'TotalSpent': 'إجمالي المبلغ المنفق',
      'TotalSales': 'إجمالي المبيعات',
      'TotalAmount': 'إجمالي المبلغ',
      'OrderCount': 'عدد الطلبات',
      'SalesDate': 'تاريخ المبيعات',
      'AveragePrice': 'متوسط السعر',
      'MaxPrice': 'أعلى سعر',
      'MinPrice': 'أقل سعر',
      'ProductCount': 'عدد المنتجات',
      'CustomerCount': 'عدد العملاء',

      // أعمدة إضافية
      'ID': 'المعرف',
      'ItemID': 'معرف المنتج',
      'ClientID': 'معرف العميل',
      'BranchID': 'معرف الفرع',
      'SerialNumber': 'الرقم التسلسلي',
      'Barcode': 'الباركود'
    };

    return translations[columnName] || columnName;
  };

  if (queryHistory.length === 0) {
    return (
      <div className="text-center py-12">
        <Table className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          لا توجد نتائج بعد
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          ابدأ بطرح سؤال لرؤية النتائج والتحليلات هنا
        </p>
      </div>
    );
  }

  const renderChart = (data: any[], type: string = 'bar') => {
    if (!data || data.length === 0) return null;

    const keys = Object.keys(data[0]);
    const xKey = keys[0];
    const yKey = keys.find(key => typeof data[0][key] === 'number') || keys[1];

    switch (type) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey={yKey} stroke="#8884d8" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        );
      
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey={yKey}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );
      
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey={yKey} fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        );
    }
  };

  const renderTable = (data: any[]) => {
    if (!data || data.length === 0) return null;

    const columns = Object.keys(data[0]);

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              {columns.map((column) => (
                <th
                  key={column}
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {translateColumnName(column)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                {columns.map((column) => (
                  <td
                    key={column}
                    className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                  >
                    {typeof row[column] === 'number' 
                      ? row[column].toLocaleString('ar-SA')
                      : row[column]
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const exportData = () => {
    if (!selectedResult) return;
    
    const csvContent = [
      Object.keys(selectedResult.data[0]).join(','),
      ...selectedResult.data.map(row => Object.values(row).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `query-result-${selectedResult.id}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Query History Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              سجل الاستعلامات
            </h3>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {queryHistory.map((result) => (
              <button
                key={result.id}
                onClick={() => setSelectedResult(result)}
                className={`w-full text-right p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                  selectedResult?.id === result.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
              >
                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {result.query}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {result.timestamp.toLocaleString('ar-SA')}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Results Area */}
      <div className="lg:col-span-3">
        {selectedResult && (
          <div className="space-y-6">
            {/* Query Info */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {selectedResult.query}
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    تم التنفيذ في: {selectedResult.timestamp.toLocaleString('ar-SA')}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={exportData}
                    className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                  >
                    <Download className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => onQuerySelect(selectedResult)}
                    className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                  >
                    <Eye className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* View Mode Tabs */}
              <div className="flex space-x-4 mb-6">
                <button
                  onClick={() => setViewMode('table')}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    viewMode === 'table'
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
                  }`}
                >
                  <Table className="w-4 h-4 inline mr-2" />
                  جدول
                </button>
                <button
                  onClick={() => setViewMode('chart')}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    viewMode === 'chart'
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
                  }`}
                >
                  <TrendingUp className="w-4 h-4 inline mr-2" />
                  رسم بياني
                </button>
                <button
                  onClick={() => setViewMode('sql')}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    viewMode === 'sql'
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
                  }`}
                >
                  <Code className="w-4 h-4 inline mr-2" />
                  SQL
                </button>
              </div>

              {/* Content */}
              {viewMode === 'table' && renderTable(selectedResult.data)}
              {viewMode === 'chart' && renderChart(selectedResult.data, selectedResult.visualization)}
              {viewMode === 'sql' && (
                <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto text-sm">
                  <code className="text-gray-800 dark:text-gray-200">
                    {selectedResult.sql}
                  </code>
                </pre>
              )}
            </div>

            {/* Analysis */}
            {selectedResult.analysis && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  التحليل والرؤى
                </h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {selectedResult.analysis}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
