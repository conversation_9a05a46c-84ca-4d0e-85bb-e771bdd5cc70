{"queries": [{"id": "07e9c331e4db891e769cee02b7f49457", "query": "اكثر ثلاثة منتجات مبيعاً ", "sql": "SELECT TOP 3 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalSold FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' GROUP BY tbltemp_ItemsMain.ItemName ORDER BY TotalSold DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalSold": 363}, {"ItemName": "ميكروويف", "TotalSold": 346}, {"ItemName": "طاولة جانبية", "TotalSold": 344}], "analysis": "القرص الصلب 1TB يُظهر أفضل أداء بفارق ضئيل (3%) مقارنة بالمتوسط، مما يشير إلى احتمال وجود طلب قوي على حلول التخزين. الميكروويف يُظهر استقراراً في المبيعات كمنتج أساسي للمنزل. الطاولة الجانبية قد تكون متأثرة بالموسمية أو الحملات التسويقية. يُنصح بربط هذه المنتجات بتحليلات الابتكار الشهري لتحديد فرص تحسين التصميم أو التسعير.", "timestamp": "2025-07-31T01:40:15.375Z", "expiresAt": "2025-07-31T02:40:15.375Z", "executionTime": 35707, "dataHash": "3756e5a739609dbbfe19ecf48252e0d6"}, {"id": "0bcb76a7245c06c65e60848751e3445c", "query": "اكثر المنتجات شراء ", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity\nFROM tbltemp_ItemsMain\nWHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'\nGROUP BY tbltemp_ItemsMain.ItemName\nORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363}, {"ItemName": "ميكروويف", "TotalQuantity": 346}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330}, {"ItemName": "مكنسة كهربائية", "TotalQuantity": 327}, {"ItemName": "سماعات بلوتوث", "TotalQuantity": 322}, {"ItemName": "حبر طابعة", "TotalQuantity": 319}, {"ItemName": "مكتب خشبي", "TotalQuantity": 317}, {"ItemName": "غسالة أطباق", "TotalQuantity": 317}], "analysis": "المنتجات الإلكترونية تهيمن على 40% من أعلى 10 مبيعات، مما يشير إلى توجه قوي نحو التكنولوجيا. تُظهر السلع المنزلية (مكتب خشبي، طاولة جانبية) استقرارًا في المبيعات. تمر خلاص يُظهر أداءً مفاجئًا كمنتج غذائي ضمن أعلى 10، مما قد يعكس طلب موسمي أو ترويجًا فعالًا. متوسط المبيعات (331.5 وحدة) يُظهر تنافسية عالية بين المنتجات.", "timestamp": "2025-07-31T02:07:56.237Z", "expiresAt": "2025-07-31T03:07:56.237Z", "executionTime": 14117, "dataHash": "d671f48fe609d2de0c08729e0f08e9b5"}, {"id": "ca2f676a594b427f4eaa0af000d092e3", "query": "تفاصيل مبيعات المنتج ميكروويف", "sql": "SELECT CAST(tbltemp_ItemsMain.TheDate AS DATE) AS SalesDate, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity, SUM(tbltemp_ItemsMain.Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' AND tbltemp_ItemsMain.ItemName = 'ميكروويف' GROUP BY CAST(tbltemp_ItemsMain.TheDate AS DATE) ORDER BY SalesDate ASC", "data": [{"SalesDate": "2025-04-23T00:00:00.000Z", "TotalQuantity": 10, "TotalAmount": 3200}, {"SalesDate": "2025-04-24T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-04-25T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-04-26T00:00:00.000Z", "TotalQuantity": 13, "TotalAmount": 4160}, {"SalesDate": "2025-04-27T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-04-30T00:00:00.000Z", "TotalQuantity": 2, "TotalAmount": 640}, {"SalesDate": "2025-05-02T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-05-05T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-05-06T00:00:00.000Z", "TotalQuantity": 8, "TotalAmount": 2560}, {"SalesDate": "2025-05-07T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-05-08T00:00:00.000Z", "TotalQuantity": 6, "TotalAmount": 1920}, {"SalesDate": "2025-05-09T00:00:00.000Z", "TotalQuantity": 3, "TotalAmount": 960}, {"SalesDate": "2025-05-10T00:00:00.000Z", "TotalQuantity": 11, "TotalAmount": 3520}, {"SalesDate": "2025-05-11T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-05-14T00:00:00.000Z", "TotalQuantity": 3, "TotalAmount": 960}, {"SalesDate": "2025-05-15T00:00:00.000Z", "TotalQuantity": 3, "TotalAmount": 960}, {"SalesDate": "2025-05-17T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-05-18T00:00:00.000Z", "TotalQuantity": 7, "TotalAmount": 2240}, {"SalesDate": "2025-05-21T00:00:00.000Z", "TotalQuantity": 2, "TotalAmount": 640}, {"SalesDate": "2025-05-22T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-05-23T00:00:00.000Z", "TotalQuantity": 6, "TotalAmount": 1920}, {"SalesDate": "2025-05-24T00:00:00.000Z", "TotalQuantity": 10, "TotalAmount": 3200}, {"SalesDate": "2025-05-25T00:00:00.000Z", "TotalQuantity": 10, "TotalAmount": 3200}, {"SalesDate": "2025-05-26T00:00:00.000Z", "TotalQuantity": 6, "TotalAmount": 1920}, {"SalesDate": "2025-05-27T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-05-28T00:00:00.000Z", "TotalQuantity": 3, "TotalAmount": 960}, {"SalesDate": "2025-05-29T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-06-02T00:00:00.000Z", "TotalQuantity": 2, "TotalAmount": 640}, {"SalesDate": "2025-06-03T00:00:00.000Z", "TotalQuantity": 14, "TotalAmount": 4480}, {"SalesDate": "2025-06-04T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-06-06T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-06-10T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-06-11T00:00:00.000Z", "TotalQuantity": 9, "TotalAmount": 2880}, {"SalesDate": "2025-06-13T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-06-14T00:00:00.000Z", "TotalQuantity": 6, "TotalAmount": 1920}, {"SalesDate": "2025-06-15T00:00:00.000Z", "TotalQuantity": 10, "TotalAmount": 3200}, {"SalesDate": "2025-06-17T00:00:00.000Z", "TotalQuantity": 0, "TotalAmount": 0}, {"SalesDate": "2025-06-18T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-06-19T00:00:00.000Z", "TotalQuantity": 2, "TotalAmount": 640}, {"SalesDate": "2025-06-20T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-06-21T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-06-23T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-06-24T00:00:00.000Z", "TotalQuantity": 15, "TotalAmount": 4800}, {"SalesDate": "2025-06-25T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-06-28T00:00:00.000Z", "TotalQuantity": 3, "TotalAmount": 960}, {"SalesDate": "2025-07-01T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-07-02T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-07-03T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-07-05T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-07-06T00:00:00.000Z", "TotalQuantity": 2, "TotalAmount": 640}, {"SalesDate": "2025-07-07T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-07-08T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-07-09T00:00:00.000Z", "TotalQuantity": 8, "TotalAmount": 2560}, {"SalesDate": "2025-07-10T00:00:00.000Z", "TotalQuantity": 11, "TotalAmount": 3520}, {"SalesDate": "2025-07-11T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-07-13T00:00:00.000Z", "TotalQuantity": 12, "TotalAmount": 3840}, {"SalesDate": "2025-07-14T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-07-15T00:00:00.000Z", "TotalQuantity": 5, "TotalAmount": 1600}, {"SalesDate": "2025-07-16T00:00:00.000Z", "TotalQuantity": 9, "TotalAmount": 2880}, {"SalesDate": "2025-07-17T00:00:00.000Z", "TotalQuantity": 13, "TotalAmount": 4160}, {"SalesDate": "2025-07-18T00:00:00.000Z", "TotalQuantity": 4, "TotalAmount": 1280}, {"SalesDate": "2025-07-19T00:00:00.000Z", "TotalQuantity": 2, "TotalAmount": 640}, {"SalesDate": "2025-07-20T00:00:00.000Z", "TotalQuantity": 11, "TotalAmount": 3520}, {"SalesDate": "2025-07-22T00:00:00.000Z", "TotalQuantity": 1, "TotalAmount": 320}, {"SalesDate": "2025-07-23T00:00:00.000Z", "TotalQuantity": 12, "TotalAmount": 3840}], "analysis": "يُظهر التحليل أن المبيعات تتأثر بعوامل موسمية محتملة أو طلبات مركزة. تُظهر البيانات تركزًا في المبيعات خلال أيام العمل (الأثنين-الخميس) مع انخفاض في نهاية الأسبوع. السعر الثابت لكل وحدة (320 درهم) يشير إلى احتمال وجود عروض ترويجية أو طلبات بكميات كبيرة.", "timestamp": "2025-07-31T02:09:39.026Z", "expiresAt": "2025-07-31T03:09:39.026Z", "executionTime": 85623, "dataHash": "b8a8a5776eb7137f62a65aefdda89f4c"}], "version": "1.0.0", "lastCleanup": "2025-07-31T01:39:39.669Z"}