{"queries": [{"id": "7f48a57cb94ddb7951b49a44017b2525", "query": "تفاصيل مبيعات منتج غسالة أطباق ", "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}, {"ItemName": "مكنسة كهربائية", "TotalQuantity": 327, "TotalRevenue": 147150}, {"ItemName": "سماعات بلوتوث", "TotalQuantity": 322, "TotalRevenue": 57960}, {"ItemName": "حبر طابعة", "TotalQuantity": 319, "TotalRevenue": 30305}, {"ItemName": "مكتب خشبي", "TotalQuantity": 317, "TotalRevenue": 253600}, {"ItemName": "غسالة أطباق", "TotalQuantity": 317, "TotalRevenue": 443800}], "analysis": "1. **العائدية العالية**: الغسالة تحقق أعلى 5% من الإيرادات بين المنتجات المذكورة، مما يدل على قدرتها على جذب العملاء ذوي القدرة الشرائية العالية. 2. **التوازن بين الكمية والسعر**: رغم أن الكميات المباعة أقل من المتوسط (331.5)، فإن الإيرادات تفوق المتوسط (182,970.5)، مما يشير إلى سعر وحدة أعلى مقارنة بمنتجات أخرى. 3. **فرصة للتوسع**: يمكن استغلال الطلب على الغسالة كمحور استراتيجي للترويج أو التجميع مع منتجات مكملة مثل 'مكنسة كهربائية' أو 'ميكروويف'.", "timestamp": "2025-07-31T02:34:42.856Z", "expiresAt": "2025-07-31T03:34:42.856Z", "executionTime": 20349, "dataHash": "58b0ab81d8e7e76204b68130395137f6"}, {"id": "09754aa711ef4c67a47a43398737666f", "query": "اكثر ثلاثة عملاء شراء ", "sql": "SELECT TOP 3 i.ClientName, SUM(i.Amount) AS TotalSpent\nFROM tbltemp_ItemsMain i\nWHERE i.DocumentName = 'فاتورة مبيعات'\n  AND YEAR(i.TheDate) = YEAR(GETDATE())\nGROUP BY i.ClientName\nORDER BY TotalSpent DESC;", "data": [{"ClientName": "سمر فهد", "TotalSpent": 384265}, {"ClientName": "عباس يوسف", "TotalSpent": 341275}, {"ClientName": "سليمان صالح", "TotalSpent": 314105}], "analysis": "العملاء الثلاثة الأوائل يمثلون 98% من أعلى 3 عملاء، مما يشير إلى تركز شديد في الإنفاق. الزيادة في الإنفاق تشير إلى فرصة لتعزيز علاقات العملاء القيّمين. متوسط الإنفاق المرتفع (346,548) يعكس قوة شراء ملحوظة تتطلب استراتيجيات استبقاء.", "timestamp": "2025-07-31T02:40:13.976Z", "expiresAt": "2025-07-31T03:40:13.976Z", "executionTime": 16346, "dataHash": "6192773428e174d2de085f0f2fb4aa52"}, {"id": "2e4c2a222e5be818ba1e3d2cf2cbb8df", "query": "اكثر ثلاثة منتجات اشتراها العميل سمر فهد", "sql": "SELECT TOP 3 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalUnits\nFROM tbltemp_ItemsMain\nWHERE tbltemp_ItemsMain.ClientName = 'سمر فهد' AND tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'\nGROUP BY tbltemp_ItemsMain.ItemName\nORDER BY TotalUnits DESC;", "data": [{"ItemName": "مكتب خشبي", "TotalUnits": 37}, {"ItemName": "طاولة جانبية", "TotalUnits": 35}, {"ItemName": "ورق A4", "TotalUnits": 32}], "analysis": "تُظهر البيانات تفضيل العميلة للمنتجات ذات الاستخدام المتكرر والقيمة العالية (مثل الأثاث)، بينما يمثل 'ورق A4' احتياجًا دوريًا أقل أهمية. يُلاحظ أن حصة الأثاث في المبيعات تبلغ 85% (69 وحدة من أصل 104)، مما يشير إلى فرصة لتوسيع خطوط الأثاث أو تقديم عروض تجميعية. كما أن انخفاض مبيعات 'ورق A4' مقارنة بالمنتجات الأخرى قد يعكس اعتمادًا على موردين خارجيين.", "timestamp": "2025-07-31T02:41:31.391Z", "expiresAt": "2025-07-31T03:41:31.391Z", "executionTime": 51050, "dataHash": "ee628063cd73adb3d27c0281c8f3ddb8"}, {"id": "dac96c934a263900cfce1282af45f6d1", "query": "اعرض لي مبيعات يوم 7 من هذا الشهر ", "sql": "SELECT BranchName, SUM(Amount) AS TotalSales, COUNT(*) AS TransactionCount FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND DAY(TheDate) = 7 AND MONTH(TheDate) = MONTH(GETDATE()) AND YEAR(TheDate) = YEAR(GETDATE()) GROUP BY BranchName ORDER BY TotalSales DESC", "data": [{"BranchName": "فرع الرياض", "TotalSales": 36130, "TransactionCount": 20}], "analysis": "البيانات المتوفرة محدودة بفرع واحد ونقطة زمنية واحدة، مما يمنع تحليل اتجاهات أو مقارنات شاملة. الأداء اليومي يظهر كفاءة في العمليات (20 عملية بيع) لكن لا يمكن تقييمه دون معايير مرجعية.", "timestamp": "2025-07-31T02:43:13.164Z", "expiresAt": "2025-07-31T03:43:13.164Z", "executionTime": 60887, "dataHash": "dceb56e2f850e1b8c8a737ac102fa0e1"}, {"id": "070de7991f99f5fbfd3a28ed5846485e", "query": "تفاصيل مشتريات طاولة جانبية ", "sql": "SELECT ItemName, ClientName, Quantity, Amount, UnitPrice, TheDate, BranchName\n              FROM tbltemp_ItemsMain\n              WHERE ItemName LIKE '%محدد%'\n              AND DocumentName = 'فاتورة مبيعات'\n              ORDER BY TheDate DESC", "data": [], "analysis": "غياب البيانات يشير إما إلى عدم وجود مبيعات لهذا المنتج أو مشاكل في جمع البيانات. يُنصح بربط هذا الفراغ بالاستراتيجيات التجارية لتحديد ما إذا كان المنتج في مرحلة التجربة أو يحتاج إلى تحسين في التسويق.", "timestamp": "2025-07-31T02:44:05.904Z", "expiresAt": "2025-07-31T03:44:05.904Z", "executionTime": 5591, "dataHash": "d751713988987e9331980363e24189ce"}, {"id": "17ef24e3c544440fc07886b1133044b9", "query": "اريد تفاصيل مبيعاتنا من مكتب خشبي ", "sql": "SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, TheDate), 0) AS Month, BranchName, SUM(Amount) AS TotalSales FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND ItemName = 'مكتب خشبي' GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, TheDate), 0), BranchName ORDER BY Month", "data": [{"Month": "2025-04-01T00:00:00.000Z", "BranchName": "فرع الرياض", "TotalSales": 21600}, {"Month": "2025-05-01T00:00:00.000Z", "BranchName": "فرع الرياض", "TotalSales": 78400}, {"Month": "2025-06-01T00:00:00.000Z", "BranchName": "فرع الرياض", "TotalSales": 95200}, {"Month": "2025-07-01T00:00:00.000Z", "BranchName": "فرع الرياض", "TotalSales": 58400}], "analysis": "البيانات تشير إلى اعتماد شديد على فرع واحد (الرياض) مع عدم وجود بيانات مقارنة من فروع أخرى. الزيادة المفاجئة في مايو تشير إلى حملة تسويقية محتملة أو طلب موسمي. التراجع في يوليو يعكس احتمالية نقص المخزون أو تغير في سلوك المستهلك. تتوافق النتائج مع متوسطات السوق في قطاع الأثاث الخشبي حيث تتراوح المبيعات بين 50,000-100,000 شهريًا.", "timestamp": "2025-07-31T02:45:40.096Z", "expiresAt": "2025-07-31T03:45:40.096Z", "executionTime": 54198, "dataHash": "af98c1ca4bab27dfcb5ac3ba0aaa7342"}, {"id": "1605349870f7750bdccd3106b149012e", "query": "تفاصيل مبيعات المنتج حبر طابعة ", "sql": "SELECT TheDate, SUM(Amount) AS TotalAmount, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND ItemName LIKE '%حبر طابعة%' GROUP BY TheDate ORDER BY TheDate", "data": [{"TheDate": "2025-04-23T20:54:00.870Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-04-24T05:25:00.480Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-04-24T09:14:44.287Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-04-26T10:16:56.043Z", "TotalAmount": -95, "TotalQuantity": -1}, {"TheDate": "2025-04-26T11:21:24.643Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-04-26T23:32:25.787Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-04-27T21:16:26.397Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-04-28T01:20:08.387Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-04-30T06:01:51.480Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-04-30T16:51:00.757Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-02T07:49:01.183Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-04T07:04:49.270Z", "TotalAmount": -190, "TotalQuantity": -2}, {"TheDate": "2025-05-04T21:57:57.577Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-05T07:08:06.970Z", "TotalAmount": -285, "TotalQuantity": -3}, {"TheDate": "2025-05-05T07:10:15.157Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-06T08:05:01.210Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-06T09:01:09.850Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-06T09:34:14.107Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-06T13:45:21.680Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-06T22:28:20.107Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-05-08T05:44:19.470Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-08T10:44:22.620Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-09T17:48:52.520Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-05-10T05:50:04.553Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-10T19:10:55.290Z", "TotalAmount": -285, "TotalQuantity": -3}, {"TheDate": "2025-05-12T11:44:55.027Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-12T17:09:53.220Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-12T19:49:20.757Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-14T20:55:28.487Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-14T22:21:14.687Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-15T03:09:03.410Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-15T12:09:27.740Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-15T23:13:45.470Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-16T12:56:35.457Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-16T16:22:12.527Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-17T14:48:18.607Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-17T17:16:04.100Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-17T22:54:38.863Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-18T14:59:13.653Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-18T23:45:22.603Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-19T03:29:01.693Z", "TotalAmount": -95, "TotalQuantity": -1}, {"TheDate": "2025-05-21T12:59:04.903Z", "TotalAmount": -285, "TotalQuantity": -3}, {"TheDate": "2025-05-22T01:30:53.533Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-23T11:15:34.990Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-24T05:42:43.267Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-24T12:48:18.020Z", "TotalAmount": 855, "TotalQuantity": 9}, {"TheDate": "2025-05-24T14:39:17.470Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-25T11:55:51.840Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-25T23:11:09.930Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-05-26T06:47:47.897Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-28T01:00:34.800Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-05-28T10:18:44.960Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-28T10:53:01.410Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-05-29T17:23:43.920Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-05-30T00:54:01.497Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-05-30T03:34:14.457Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-05-30T20:22:52.140Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-01T16:50:05.797Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-06-01T21:28:26.957Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-06-03T02:34:07.013Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-03T14:41:04.870Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-06-03T19:25:53.107Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-06-04T00:34:10.443Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-04T05:57:01.680Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-04T22:24:02.637Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-05T09:34:45.703Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-06-05T14:55:24.303Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-07T15:12:01.420Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-06-08T02:02:48.573Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-06-08T05:08:58.290Z", "TotalAmount": -190, "TotalQuantity": -2}, {"TheDate": "2025-06-08T18:11:21.713Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-09T05:58:27.270Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-13T21:23:11.453Z", "TotalAmount": -190, "TotalQuantity": -2}, {"TheDate": "2025-06-13T21:44:18.187Z", "TotalAmount": -285, "TotalQuantity": -3}, {"TheDate": "2025-06-14T03:39:56.880Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-14T06:46:03.223Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-15T09:41:28.610Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-06-15T22:11:10.037Z", "TotalAmount": -190, "TotalQuantity": -2}, {"TheDate": "2025-06-16T01:26:18.253Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-06-16T02:05:18.940Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-06-20T03:05:39.270Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-20T08:26:24.437Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-20T12:44:45.040Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-06-23T14:47:07.110Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-23T23:12:03.683Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-24T08:07:47.040Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-06-25T00:44:20.247Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-06-25T02:02:00.577Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-25T12:19:19.987Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-26T01:02:25.617Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-06-26T03:12:33.537Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-06-27T06:55:16.370Z", "TotalAmount": -95, "TotalQuantity": -1}, {"TheDate": "2025-06-28T13:43:02.347Z", "TotalAmount": 0, "TotalQuantity": 0}, {"TheDate": "2025-06-30T09:34:08.183Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-06-30T14:21:18.973Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-02T00:48:43.177Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-07-02T18:11:59.857Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-04T04:10:10.983Z", "TotalAmount": 570, "TotalQuantity": 6}, {"TheDate": "2025-07-04T09:49:41.307Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-06T06:18:48.737Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-07-06T07:52:58.650Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-07T09:58:18.377Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-07-08T00:09:27.283Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-07-09T23:23:11.877Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-07-10T07:41:21.117Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-07-12T17:28:32.037Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-07-13T08:03:41.583Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-07-14T04:09:23.233Z", "TotalAmount": -285, "TotalQuantity": -3}, {"TheDate": "2025-07-14T08:58:03.273Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-07-16T04:36:32.690Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-07-16T07:26:38.580Z", "TotalAmount": 190, "TotalQuantity": 2}, {"TheDate": "2025-07-16T13:30:32.937Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-16T21:14:25.713Z", "TotalAmount": 285, "TotalQuantity": 3}, {"TheDate": "2025-07-17T08:56:34.050Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-17T11:09:09.273Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-18T05:42:09.040Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-07-19T12:33:55.583Z", "TotalAmount": 95, "TotalQuantity": 1}, {"TheDate": "2025-07-19T13:22:26.187Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-22T10:31:02.107Z", "TotalAmount": 380, "TotalQuantity": 4}, {"TheDate": "2025-07-23T12:08:33.160Z", "TotalAmount": 475, "TotalQuantity": 5}, {"TheDate": "2025-07-23T19:54:23.483Z", "TotalAmount": 380, "TotalQuantity": 4}], "analysis": "تُظهر البيانات أن متوسط المبيعات اليومية يبلغ 3,367 ريالات، مع تركز المبيعات العالية في أوائل أبريل. القيم السلبية في المبيعات (مثل -95 ريالات) قد تشير إلى إرجاعات أو أخطاء في النظام تتطلب التحقق منها. يُلاحظ أن أعلى مبيعات يومية بلغت 855 ريالات (9 وحدات) في 26 أبريل، مما يشير إلى فرصة لزيادة المبيعات عبر تحسين تجربة العملاء.", "timestamp": "2025-07-31T02:56:35.298Z", "expiresAt": "2025-07-31T03:56:35.298Z", "executionTime": 51344, "dataHash": "052074a3d6053db7474275fe521fa7b6"}, {"id": "7a8f3e5a9580fd49c1f2a38830dbfd1d", "query": "اكثر المنتجات مبيعاً ", "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalSales FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND YEAR(TheDate) = YEAR(GETDATE()) GROUP BY ItemName ORDER BY TotalSales DESC", "data": [{"ItemName": "هاتف آيفون", "TotalQuantity": 296, "TotalSales": 1243200}, {"ItemName": "لابتوب HP", "TotalQuantity": 278, "TotalSales": 973000}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalSales": 594000}, {"ItemName": "غسالة أطباق", "TotalQuantity": 317, "TotalSales": 443800}, {"ItemName": "شاشة سامسونج", "TotalQuantity": 258, "TotalSales": 309600}, {"ItemName": "مكتب خشبي", "TotalQuantity": 317, "TotalSales": 253600}, {"ItemName": "كرسي مكتب", "TotalQuantity": 292, "TotalSales": 189800}, {"ItemName": "مكنسة كهربائية", "TotalQuantity": 327, "TotalSales": 147150}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalSales": 110720}, {"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalSales": 101640}], "analysis": "يظهر توازن بين كمية المبيعات والعائد المالي في المنتجات القيادية، حيث يحقق 'هاتف آيفون' أعلى العائد رغم كونه ثاني أعلى في الكمية. بينما تتفوق 'الغسالة الأطباق' على 'الشاشة سامسونج' في الكمية لكنها أقل في العائد. تشير البيانات إلى أن المنتجات ذات السعر المرتفع (مثل الهواتف والأجهزة اللوحية) تحقق أعلى عائد مالي.", "timestamp": "2025-07-31T03:07:16.888Z", "expiresAt": "2025-07-31T04:07:16.888Z", "executionTime": 28665, "dataHash": "724dddc7812a8cd945dcf5ecf66d2d1f"}, {"id": "4c952c2de6da2eaad29b467670eabac3", "query": "اكثر المنتجات مبيعاً", "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND YEAR(TheDate) = YEAR(GETDATE()) GROUP BY ItemName ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}, {"ItemName": "مكنسة كهربائية", "TotalQuantity": 327, "TotalRevenue": 147150}, {"ItemName": "سماعات بلوتوث", "TotalQuantity": 322, "TotalRevenue": 57960}, {"ItemName": "حبر طابعة", "TotalQuantity": 319, "TotalRevenue": 30305}, {"ItemName": "مكتب خشبي", "TotalQuantity": 317, "TotalRevenue": 253600}, {"ItemName": "غسالة أطباق", "TotalQuantity": 317, "TotalRevenue": 443800}], "analysis": "يظهر تباين في الأداء بين المنتجات حسب الفئة: المنتجات المنزلية تحقق أعلى إيرادات رغم كميات متوسطة، بينما المنتجات الغذائية (تمر خلاص) تحقق كميات جيدة لكن إيراداتها منخفضة. متوسط الإيرادات لكل منتج (182,970.5) يشير إلى فرص تحسين في المنتجات ذات الكمية العالية ولكن الإيراد المنخفض مثل الحبر الطابعة.", "timestamp": "2025-07-31T03:19:16.165Z", "expiresAt": "2025-07-31T04:19:16.165Z", "executionTime": 42523, "dataHash": "58b0ab81d8e7e76204b68130395137f6"}, {"id": "c432e47a71b5f80a7fa700c9aa9279e1", "query": "المنتج الاكثر ايراد", "sql": "SELECT TOP 10 ItemName, SUM(Amount) AS TotalRevenue FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalRevenue DESC", "data": [{"ItemName": "هاتف آيفون", "TotalRevenue": 1243200}, {"ItemName": "لابتوب HP", "TotalRevenue": 973000}, {"ItemName": "مكي<PERSON> هواء", "TotalRevenue": 594000}, {"ItemName": "غسالة أطباق", "TotalRevenue": 443800}, {"ItemName": "شاشة سامسونج", "TotalRevenue": 309600}, {"ItemName": "مكتب خشبي", "TotalRevenue": 253600}, {"ItemName": "كرسي مكتب", "TotalRevenue": 189800}, {"ItemName": "مكنسة كهربائية", "TotalRevenue": 147150}, {"ItemName": "ميكروويف", "TotalRevenue": 110720}, {"ItemName": "قرص صلب 1TB", "TotalRevenue": 101640}], "analysis": "يُلاحظ أن 50% من إجمالي الإيرادات تأتي من منتجين فقط، مما يُشير إلى اعتماد كبير على أداء 'هاتف آيفون' و'لابتوب HP'. تراجع الإيرادات بنسبة 57% بين المرتبة الأولى والثانية يُظهر غياب توازن في المحفظة. المنتجات الخفيفة مثل 'مكنسة كهربائية' و'ميكروويف' تُسهم بـ 3.4% فقط من الإيرادات، مما يُشير إلى فرص لتحسين الأداء.", "timestamp": "2025-07-31T03:22:29.110Z", "expiresAt": "2025-07-31T04:22:29.110Z", "executionTime": 19357, "dataHash": "3168b922575cbf9cfb1d42cf074f2f71"}, {"id": "2a167aaa48a6fe8a948b9f009e3d531d", "query": "المنتج الاكثر مبيعاً من حيث الكمية ", "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}, {"ItemName": "مكنسة كهربائية", "TotalQuantity": 327, "TotalRevenue": 147150}, {"ItemName": "سماعات بلوتوث", "TotalQuantity": 322, "TotalRevenue": 57960}, {"ItemName": "حبر طابعة", "TotalQuantity": 319, "TotalRevenue": 30305}, {"ItemName": "مكتب خشبي", "TotalQuantity": 317, "TotalRevenue": 253600}, {"ItemName": "غسالة أطباق", "TotalQuantity": 317, "TotalRevenue": 443800}], "analysis": "المنتج الأكثر مبيعًا يُظهر تفوقًا في الكمية مقارنة بالمتوسط (331.5 وحدة)، لكن إيراداته أقل من منتجات أخرى مثل 'مكيف هواء' الذي يحقق إيرادات أعلى رغم كميته المماثلة. هذا يعكس فجوة في استراتيجية التسعير أو تركيب المبيعات. كما أن التوزيع المتساوي في الكميات (317-363) يشير إلى تنافسية عالية بين المنتجات.", "timestamp": "2025-07-31T03:23:38.665Z", "expiresAt": "2025-07-31T04:23:38.665Z", "executionTime": 6916, "dataHash": "58b0ab81d8e7e76204b68130395137f6"}], "version": "1.0.0", "lastCleanup": "2025-07-31T03:22:09.754Z"}