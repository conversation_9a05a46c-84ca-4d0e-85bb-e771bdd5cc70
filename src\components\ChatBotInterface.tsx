'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, Bar<PERSON>hart3, Table, Code } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'result';
  content: string;
  timestamp: number;
  data?: any[];
  sql?: string;
  analysis?: string;
  query?: string;
}

interface ChatBotInterfaceProps {
  connectionData: any;
  messages?: ChatMessage[];
  onMessagesChange?: (messages: ChatMessage[]) => void;
  onQueryResult?: (result: any) => void;
}

export default function ChatBotInterface({
  connectionData,
  messages: externalMessages = [],
  onMessagesChange,
  onQueryResult
}: ChatBotInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>(externalMessages);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // تحديث المحادثات عند تغيير المحادثات الخارجية
  useEffect(() => {
    setMessages(externalMessages);
  }, [externalMessages]);

  // تحديث المحادثات الخارجية عند تغيير المحادثات المحلية
  useEffect(() => {
    if (onMessagesChange) {
      onMessagesChange(messages);
    }
  }, [messages, onMessagesChange]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/query/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: currentInput,
          connectionData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        const resultMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'result',
          content: result.analysis || 'تم تنفيذ الاستعلام بنجاح',
          timestamp: Date.now(),
          data: result.data,
          sql: result.sql,
          analysis: result.analysis,
          query: currentInput
        };

        setMessages(prev => [...prev, resultMessage]);
        
        // إرسال النتيجة للمكون الأب
        if (onQueryResult) {
          onQueryResult({
            id: resultMessage.id,
            query: currentInput,
            sql: result.sql,
            data: result.data,
            analysis: result.analysis,
            timestamp: new Date(),
            visualization: result.visualization,
            success: true
          });
        }
      } else {
        const errorMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: `عذراً، حدث خطأ: ${result.error}`,
          timestamp: Date.now()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('خطأ في الشبكة:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.',
        timestamp: Date.now()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const ResultMessage = ({ message }: { message: ChatMessage }) => {
    const [viewMode, setViewMode] = useState<'table' | 'chart' | 'sql'>('table');

    // دالة لترجمة أسماء الأعمدة إلى العربية
    const translateColumnName = (columnName: string): string => {
      const translations: { [key: string]: string } = {
        // أسماء الأعمدة الأساسية
        'ItemName': 'اسم المنتج',
        'ClientName': 'اسم العميل',
        'BranchName': 'اسم الفرع',
        'CategoryName': 'اسم الفئة',
        'DocumentName': 'نوع الوثيقة',
        'TheDate': 'التاريخ',
        'Quantity': 'الكمية',
        'Amount': 'المبلغ',
        'UnitPrice': 'سعر الوحدة',

        // أسماء الأعمدة المحسوبة
        'TotalQuantity': 'إجمالي الكمية',
        'TotalSpent': 'إجمالي المبلغ المنفق',
        'TotalSales': 'إجمالي المبيعات',
        'TotalAmount': 'إجمالي المبلغ',
        'OrderCount': 'عدد الطلبات',
        'SalesDate': 'تاريخ المبيعات',
        'AveragePrice': 'متوسط السعر',
        'MaxPrice': 'أعلى سعر',
        'MinPrice': 'أقل سعر',
        'ProductCount': 'عدد المنتجات',
        'CustomerCount': 'عدد العملاء',

        // أعمدة إضافية
        'ID': 'المعرف',
        'ItemID': 'معرف المنتج',
        'ClientID': 'معرف العميل',
        'BranchID': 'معرف الفرع',
        'SerialNumber': 'الرقم التسلسلي',
        'Barcode': 'الباركود'
      };

      return translations[columnName] || columnName;
    };

    const renderTable = (data: any[]) => {
      if (!data || data.length === 0) return <p>لا توجد بيانات</p>;

      const columns = Object.keys(data[0]);
      
      return (
        <div className="overflow-x-auto w-full">
          <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                {columns.map((column) => (
                  <th key={column} className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {translateColumnName(column)}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {data.slice(0, 10).map((row, index) => (
                <tr key={index}>
                  {columns.map((column) => (
                    <td key={column} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {typeof row[column] === 'number' ? row[column].toLocaleString('ar-SA') : row[column]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          {data.length > 10 && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 text-center">
              عرض 10 من أصل {data.length} صف
            </p>
          )}
        </div>
      );
    };

    const renderChart = (data: any[]) => {
      if (!data || data.length === 0) return <p>لا توجد بيانات للرسم البياني</p>;

      const columns = Object.keys(data[0]);
      const numericColumns = columns.filter(col =>
        data.some(row => typeof row[col] === 'number')
      );

      if (numericColumns.length === 0) return <p>لا توجد بيانات رقمية للرسم البياني</p>;

      // إنشاء بيانات مترجمة للرسم البياني
      const translatedData = data.slice(0, 10).map(row => {
        const translatedRow: any = {};
        for (const [key, value] of Object.entries(row)) {
          const arabicKey = translateColumnName(key);
          translatedRow[arabicKey] = value;
        }
        return translatedRow;
      });

      const translatedColumns = columns.map(col => translateColumnName(col));
      const translatedNumericColumns = numericColumns.map(col => translateColumnName(col));

      return (
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={translatedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={translatedColumns[0]} />
              <YAxis />
              <Tooltip />
              <Bar dataKey={translatedNumericColumns[0]} fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      );
    };

    return (
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800 max-w-none">
        <div className="flex items-center mb-3">
          <BarChart3 className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <span className="font-medium text-blue-800 dark:text-blue-200">نتائج الاستعلام</span>
        </div>
        
        <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">{message.content}</p>
        
        {message.data && message.data.length > 0 && (
          <>
            <div className="flex space-x-2 mb-4">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 rounded text-sm ${
                  viewMode === 'table'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <Table className="w-4 h-4 inline mr-1" />
                جدول
              </button>
              <button
                onClick={() => setViewMode('chart')}
                className={`px-3 py-1 rounded text-sm ${
                  viewMode === 'chart'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <BarChart3 className="w-4 h-4 inline mr-1" />
                رسم بياني
              </button>
              <button
                onClick={() => setViewMode('sql')}
                className={`px-3 py-1 rounded text-sm ${
                  viewMode === 'sql'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <Code className="w-4 h-4 inline mr-1" />
                SQL
              </button>
            </div>

            {viewMode === 'table' && renderTable(message.data)}
            {viewMode === 'chart' && renderChart(message.data)}
            {viewMode === 'sql' && (
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-x-auto">
                <code className="text-gray-800 dark:text-gray-200">{message.sql}</code>
              </pre>
            )}
          </>
        )}
      </div>
    );
  };

  const quickSuggestions = [
    'أعرض لي أكثر 5 منتجات مبيعاً',
    'قارن بين مبيعات هذا الشهر والشهر الماضي',
    'ما هي أفضل الفروع أداءً؟',
    'أكثر العملاء شراءً في الربع الأخير'
  ];

  return (
    <div className="flex flex-col h-[calc(100vh-120px)] bg-white dark:bg-gray-900">
      {/* منطقة المحادثة */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {/* رسالة ترحيبية */}
        {messages.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
              مرحباً! أنا وكيل الذكاء الاصطناعي
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
              اسأل أي سؤال عن بياناتك وسأقوم بتحليلها وعرض النتائج لك. يمكنك البدء بأحد الأمثلة أدناه:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {quickSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setInputValue(suggestion)}
                  className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-200 text-blue-700 dark:text-blue-300 text-right border border-blue-200 dark:border-blue-800 hover:shadow-md"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* الرسائل */}
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type === 'result' ? (
              <div className="w-full max-w-4xl">
                <ResultMessage message={message} />
              </div>
            ) : (
              <div className="flex items-start space-x-3 max-w-3xl">
                {message.type === 'assistant' && (
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                )}
                <div
                  className={`rounded-2xl px-4 py-3 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white ml-auto'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                  }`}
                >
                  <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                </div>
                {message.type === 'user' && (
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {/* مؤشر الكتابة */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-2xl px-4 py-3">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">جاري التحليل...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* منطقة الإدخال */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-3">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="اكتب سؤالك هنا..."
              className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-right"
              disabled={isLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none"
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
