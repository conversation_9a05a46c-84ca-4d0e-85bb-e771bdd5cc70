'use client';

import React, { useState } from 'react';
import { X, Printer, Download, BarChart3, Table, Code, Copy } from 'lucide-react';
import EnhancedChart from './EnhancedChart';

interface QueryResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  result: {
    id: string;
    query: string;
    response: string;
    data: any[];
    sql: string;
    analysis: string;
    timestamp: Date;
  };
}

export default function QueryResultModal({ isOpen, onClose, result }: QueryResultModalProps) {
  const [activeTab, setActiveTab] = useState<'summary' | 'data' | 'chart' | 'sql'>('summary');

  if (!isOpen) return null;

  // دالة لترجمة أسماء الأعمدة إلى العربية
  const translateColumnName = (columnName: string): string => {
    const translations: { [key: string]: string } = {
      'ItemName': 'اسم المنتج',
      'ClientName': 'اسم العميل',
      'BranchName': 'اسم الفرع',
      'CategoryName': 'اسم الفئة',
      'DocumentName': 'نوع الوثيقة',
      'TheDate': 'التاريخ',
      'Quantity': 'الكمية',
      'Amount': 'المبلغ',
      'UnitPrice': 'سعر الوحدة',
      'TotalQuantity': 'إجمالي الكمية',
      'TotalSpent': 'إجمالي المبلغ المنفق',
      'TotalSales': 'إجمالي المبيعات',
      'TotalAmount': 'إجمالي المبلغ',
      'OrderCount': 'عدد الطلبات',
      'SalesDate': 'تاريخ المبيعات',
      'AveragePrice': 'متوسط السعر',
      'MaxPrice': 'أعلى سعر',
      'MinPrice': 'أقل سعر',
      'ProductCount': 'عدد المنتجات',
      'CustomerCount': 'عدد العملاء',
      'ID': 'المعرف',
      'ItemID': 'معرف المنتج',
      'ClientID': 'معرف العميل',
      'BranchID': 'معرف الفرع',
      'SerialNumber': 'الرقم التسلسلي',
      'Barcode': 'الباركود'
    };
    return translations[columnName] || columnName;
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    if (!result.data || result.data.length === 0) return;
    
    const csvContent = [
      Object.keys(result.data[0]).map(col => translateColumnName(col)).join(','),
      ...result.data.map(row => Object.values(row).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `نتائج-الاستعلام-${result.id}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderTable = () => {
    if (!result.data || result.data.length === 0) {
      return <div className="text-center py-8 text-gray-500">لا توجد بيانات للعرض</div>;
    }

    const columns = Object.keys(result.data[0]);

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              {columns.map((column) => (
                <th
                  key={column}
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {translateColumnName(column)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {result.data.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                {columns.map((column) => (
                  <td
                    key={column}
                    className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                  >
                    {typeof row[column] === 'number' 
                      ? row[column].toLocaleString('ar-SA')
                      : row[column]
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-6 print:hidden">
            <div className="flex space-x-2">
              <button
                onClick={handlePrint}
                className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                title="طباعة"
              >
                <Printer className="w-5 h-5" />
              </button>
              <button
                onClick={handleDownload}
                className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                title="تحميل CSV"
              >
                <Download className="w-5 h-5" />
              </button>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              تفاصيل نتائج الاستعلام
            </h2>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Query Info */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
              الاستعلام
            </h3>
            <p className="text-blue-800 dark:text-blue-200 mb-2">{result.query}</p>
            <p className="text-sm text-blue-600 dark:text-blue-300">
              تم التنفيذ في: {result.timestamp.toLocaleString('ar-SA')}
            </p>
          </div>

          {/* Response Summary */}
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
              الإجابة
            </h3>
            <p className="text-green-800 dark:text-green-200">{result.response}</p>
          </div>

          {/* Tabs */}
          <div className="mb-6 print:hidden">
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('summary')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'summary'
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <BarChart3 className="w-4 h-4 inline mr-2" />
                ملخص
              </button>
              <button
                onClick={() => setActiveTab('data')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'data'
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Table className="w-4 h-4 inline mr-2" />
                البيانات
              </button>
              <button
                onClick={() => setActiveTab('chart')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'chart'
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <BarChart3 className="w-4 h-4 inline mr-2" />
                الرسم البياني
              </button>
              <button
                onClick={() => setActiveTab('sql')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'sql'
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Code className="w-4 h-4 inline mr-2" />
                SQL
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="min-h-96">
            {activeTab === 'summary' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      إحصائيات البيانات
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">عدد الصفوف:</span>
                        <span className="font-medium">{result.data?.length || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">عدد الأعمدة:</span>
                        <span className="font-medium">
                          {result.data && result.data.length > 0 ? Object.keys(result.data[0]).length : 0}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {result.data && result.data.length > 0 && (
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                        الأعمدة
                      </h4>
                      <div className="space-y-1 text-sm">
                        {Object.keys(result.data[0]).map(col => (
                          <div key={col} className="text-gray-600 dark:text-gray-400">
                            {translateColumnName(col)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {result.analysis && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                      التحليل
                    </h4>
                    <p className="text-yellow-800 dark:text-yellow-200">{result.analysis}</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'data' && renderTable()}

            {activeTab === 'chart' && result.data && result.data.length > 0 && (
              <EnhancedChart data={result.data} title="الرسم البياني للنتائج" />
            )}

            {activeTab === 'sql' && (
              <div className="bg-gray-900 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold text-white">استعلام SQL</h4>
                  <button
                    onClick={() => copyToClipboard(result.sql)}
                    className="p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-800"
                    title="نسخ"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <pre className="text-green-400 text-sm overflow-x-auto">
                  <code>{result.sql}</code>
                </pre>
              </div>
            )}
          </div>

          {/* Print-only content */}
          <div className="hidden print:block">
            <div className="mb-6">
              <h3 className="text-lg font-bold mb-2">البيانات</h3>
              {renderTable()}
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-bold mb-2">استعلام SQL</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
                <code>{result.sql}</code>
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
