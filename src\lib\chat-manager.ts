// نظام إدارة الدردشات والنتائج

export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any[];
  sql?: string;
  analysis?: string;
  visualization?: string;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface QueryResult {
  id: string;
  chatId: string;
  messageId: string;
  query: string;
  response: string;
  data: any[];
  sql: string;
  analysis: string;
  visualization: string;
  timestamp: Date;
}

class ChatManager {
  private sessions: Map<string, ChatSession> = new Map();
  private results: Map<string, QueryResult> = new Map();
  private currentSessionId: string | null = null;

  constructor() {
    // تأخير تحميل البيانات حتى يكون المتصفح جاهزاً
    if (typeof window !== 'undefined') {
      this.loadFromStorage();
    }
  }

  // إنشاء دردشة جديدة
  createNewSession(title?: string): ChatSession {
    const id = this.generateId();
    const session: ChatSession = {
      id,
      title: title || `دردشة ${new Date().toLocaleDateString('ar-SA')}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.sessions.set(id, session);
    this.currentSessionId = id;
    this.saveToStorage();
    return session;
  }

  // الحصول على الدردشة الحالية
  getCurrentSession(): ChatSession | null {
    if (!this.currentSessionId) {
      return this.createNewSession();
    }
    return this.sessions.get(this.currentSessionId) || null;
  }

  // تبديل الدردشة الحالية
  switchToSession(sessionId: string): ChatSession | null {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.currentSessionId = sessionId;
      this.saveToStorage();
    }
    return session;
  }

  // إضافة رسالة للدردشة الحالية
  addMessage(message: Omit<ChatMessage, 'id' | 'timestamp'>): ChatMessage {
    const session = this.getCurrentSession();
    if (!session) throw new Error('لا توجد دردشة نشطة');

    const newMessage: ChatMessage = {
      ...message,
      id: this.generateId(),
      timestamp: new Date()
    };

    session.messages.push(newMessage);
    session.updatedAt = new Date();
    this.saveToStorage();

    return newMessage;
  }

  // حفظ نتيجة استعلام
  saveQueryResult(result: Omit<QueryResult, 'id' | 'timestamp'>): QueryResult {
    const newResult: QueryResult = {
      ...result,
      id: this.generateId(),
      timestamp: new Date()
    };

    this.results.set(newResult.id, newResult);
    this.saveToStorage();
    return newResult;
  }

  // الحصول على جميع الدردشات
  getAllSessions(): ChatSession[] {
    return Array.from(this.sessions.values())
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  // الحصول على نتيجة محددة
  getQueryResult(resultId: string): QueryResult | null {
    return this.results.get(resultId) || null;
  }

  // الحصول على جميع النتائج لدردشة معينة
  getSessionResults(sessionId: string): QueryResult[] {
    return Array.from(this.results.values())
      .filter(result => result.chatId === sessionId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // حذف دردشة
  deleteSession(sessionId: string): boolean {
    const deleted = this.sessions.delete(sessionId);
    if (deleted) {
      // حذف النتائج المرتبطة بالدردشة
      Array.from(this.results.entries()).forEach(([id, result]) => {
        if (result.chatId === sessionId) {
          this.results.delete(id);
        }
      });

      // إذا كانت الدردشة المحذوفة هي الحالية، إنشاء دردشة جديدة
      if (this.currentSessionId === sessionId) {
        this.currentSessionId = null;
      }

      this.saveToStorage();
    }
    return deleted;
  }

  // حذف نتيجة محددة
  deleteQueryResult(resultId: string): boolean {
    const deleted = this.results.delete(resultId);
    if (deleted) {
      this.saveToStorage();
    }
    return deleted;
  }

  // تحديث عنوان الدردشة
  updateSessionTitle(sessionId: string, title: string): boolean {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.title = title;
      session.updatedAt = new Date();
      this.saveToStorage();
      return true;
    }
    return false;
  }

  // مسح جميع الدردشات
  clearAllSessions(): void {
    this.sessions.clear();
    this.results.clear();
    this.currentSessionId = null;
    this.saveToStorage();
  }

  // تهيئة البيانات في المتصفح
  initializeInBrowser(): void {
    if (typeof window !== 'undefined' && this.sessions.size === 0) {
      this.loadFromStorage();
    }
  }

  // إحصائيات
  getStats() {
    return {
      totalSessions: this.sessions.size,
      totalResults: this.results.size,
      currentSessionId: this.currentSessionId,
      totalMessages: Array.from(this.sessions.values())
        .reduce((sum, session) => sum + session.messages.length, 0)
    };
  }

  // حفظ في التخزين المحلي
  private saveToStorage(): void {
    try {
      if (typeof window === 'undefined') return;

      const data = {
        sessions: Array.from(this.sessions.entries()),
        results: Array.from(this.results.entries()),
        currentSessionId: this.currentSessionId
      };
      localStorage.setItem('chatManager', JSON.stringify(data, this.dateReplacer));
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
    }
  }

  // تحميل من التخزين المحلي
  private loadFromStorage(): void {
    try {
      if (typeof window === 'undefined') return;

      const data = localStorage.getItem('chatManager');
      if (data) {
        const parsed = JSON.parse(data, this.dateReviver);
        this.sessions = new Map(parsed.sessions || []);
        this.results = new Map(parsed.results || []);
        this.currentSessionId = parsed.currentSessionId || null;
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    }
  }

  // مساعد لتحويل التواريخ عند الحفظ
  private dateReplacer(key: string, value: any): any {
    if (value instanceof Date) {
      return { __type: 'Date', value: value.toISOString() };
    }
    return value;
  }

  // مساعد لاستعادة التواريخ عند التحميل
  private dateReviver(key: string, value: any): any {
    if (value && value.__type === 'Date') {
      return new Date(value.value);
    }
    return value;
  }

  // توليد معرف فريد
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// إنشاء مثيل واحد للاستخدام في التطبيق
export const chatManager = new ChatManager();
