'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, Eye, Plus, MessageSquare, Trash2, Menu, X } from 'lucide-react';
import QueryResultModal from './QueryResultModal';
import { chatManager, ChatMessage, ChatSession, QueryResult } from '@/lib/chat-manager';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

export default function EnhancedChatInterface({ connectionData }: { connectionData: ConnectionData }) {
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [allSessions, setAllSessions] = useState<ChatSession[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [selectedResult, setSelectedResult] = useState<QueryResult | null>(null);
  const [showResultModal, setShowResultModal] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // تحميل الدردشات عند بدء التشغيل
  useEffect(() => {
    // تهيئة البيانات في المتصفح
    chatManager.initializeInBrowser();

    const session = chatManager.getCurrentSession();
    setCurrentSession(session);
    setAllSessions(chatManager.getAllSessions());
  }, []);

  // التمرير للأسفل عند إضافة رسائل جديدة
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  // إنشاء دردشة جديدة
  const createNewChat = () => {
    const newSession = chatManager.createNewSession();
    setCurrentSession(newSession);
    setAllSessions(chatManager.getAllSessions());
    setShowSidebar(false);
  };

  // تبديل الدردشة
  const switchChat = (sessionId: string) => {
    const session = chatManager.switchToSession(sessionId);
    setCurrentSession(session);
    setShowSidebar(false);
  };

  // حذف دردشة
  const deleteChat = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('هل أنت متأكد من حذف هذه الدردشة؟')) {
      chatManager.deleteSession(sessionId);
      setAllSessions(chatManager.getAllSessions());
      
      // إذا كانت الدردشة المحذوفة هي الحالية، إنشاء دردشة جديدة
      if (currentSession?.id === sessionId) {
        const newSession = chatManager.createNewSession();
        setCurrentSession(newSession);
      }
    }
  };

  // إرسال رسالة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage = input.trim();
    setInput('');
    setIsLoading(true);

    try {
      // إضافة رسالة المستخدم
      const userMsg = chatManager.addMessage({
        type: 'user',
        content: userMessage
      });

      // تحديث الحالة
      setCurrentSession(chatManager.getCurrentSession());

      // إرسال الاستعلام للخادم
      const response = await fetch('/api/query/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: userMessage,
          connectionData
        })
      });

      const result = await response.json();

      if (result.success) {
        // إنشاء رد ذكي
        const smartResponse = generateSmartResponse(result);
        
        // إضافة رسالة المساعد
        const assistantMsg = chatManager.addMessage({
          type: 'assistant',
          content: smartResponse,
          data: result.data,
          sql: result.sql,
          analysis: result.analysis
        });

        // حفظ النتيجة
        const queryResult = chatManager.saveQueryResult({
          chatId: currentSession!.id,
          messageId: assistantMsg.id,
          query: userMessage,
          response: smartResponse,
          data: result.data,
          sql: result.sql,
          analysis: result.analysis,
          visualization: 'bar'
        });

        // تحديث الحالة
        setCurrentSession(chatManager.getCurrentSession());
        setAllSessions(chatManager.getAllSessions());

      } else {
        // إضافة رسالة خطأ
        chatManager.addMessage({
          type: 'assistant',
          content: `عذراً، حدث خطأ: ${result.error}`
        });
        setCurrentSession(chatManager.getCurrentSession());
      }

    } catch (error) {
      console.error('خطأ في إرسال الاستعلام:', error);
      chatManager.addMessage({
        type: 'assistant',
        content: 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.'
      });
      setCurrentSession(chatManager.getCurrentSession());
    } finally {
      setIsLoading(false);
    }
  };

  // توليد رد ذكي
  const generateSmartResponse = (result: any): string => {
    if (!result.data || result.data.length === 0) {
      return 'لم يتم العثور على نتائج لهذا الاستعلام.';
    }

    const dataCount = result.data.length;
    const columns = Object.keys(result.data[0]);
    
    // تحديد نوع الاستعلام وإنشاء رد مناسب
    if (columns.includes('ItemName') && columns.includes('TotalQuantity')) {
      const topItems = result.data.slice(0, 3).map((item: any) => 
        `${item.ItemName} (${item.TotalQuantity} وحدة)`
      ).join('، ');
      return `أكثر المنتجات مبيعاً هي: ${topItems}. تم العثور على ${dataCount} منتج إجمالاً.`;
    }
    
    if (columns.includes('ClientName') && columns.includes('TotalSpent')) {
      const topClients = result.data.slice(0, 3).map((client: any) => 
        `${client.ClientName} (${client.TotalSpent?.toLocaleString('ar-SA')} ريال)`
      ).join('، ');
      return `أكثر العملاء شراءً هم: ${topClients}. تم العثور على ${dataCount} عميل إجمالاً.`;
    }

    return `تم العثور على ${dataCount} نتيجة. يمكنك عرض التفاصيل الكاملة والرسوم البيانية بالضغط على زر "عرض التفاصيل".`;
  };

  // عرض تفاصيل النتيجة
  const showResultDetails = (message: ChatMessage) => {
    if (!message.data || !currentSession) return;

    const result: QueryResult = {
      id: message.id,
      chatId: currentSession.id,
      messageId: message.id,
      query: currentSession.messages.find(m => m.type === 'user' && 
        currentSession.messages.indexOf(m) < currentSession.messages.indexOf(message))?.content || '',
      response: message.content,
      data: message.data,
      sql: message.sql || '',
      analysis: message.analysis || '',
      visualization: 'bar',
      timestamp: message.timestamp
    };

    setSelectedResult(result);
    setShowResultModal(true);
  };

  return (
    <div className="flex h-full bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <div className={`${showSidebar ? 'translate-x-0' : '-translate-x-full'} 
        fixed inset-y-0 right-0 z-50 w-80 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">الدردشات</h2>
          <div className="flex space-x-2">
            <button
              onClick={createNewChat}
              className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg"
              title="دردشة جديدة"
            >
              <Plus className="w-5 h-5" />
            </button>
            <button
              onClick={() => setShowSidebar(false)}
              className="p-2 text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="overflow-y-auto h-full pb-20">
          {allSessions.map((session) => (
            <div
              key={session.id}
              onClick={() => switchChat(session.id)}
              className={`p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                currentSession?.id === session.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {session.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {session.messages.length} رسالة • {new Date(session.updatedAt).toLocaleDateString('ar-SA')}
                  </p>
                </div>
                <button
                  onClick={(e) => deleteChat(session.id, e)}
                  className="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setShowSidebar(true)}
            className="p-2 text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden"
          >
            <Menu className="w-5 h-5" />
          </button>
          
          <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
            {currentSession?.title || 'دردشة جديدة'}
          </h1>
          
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-gray-500" />
            <span className="text-sm text-gray-500">
              {currentSession?.messages.length || 0} رسالة
            </span>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {currentSession?.messages.map((message) => (
            <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}>
              <div className={`max-w-3xl ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                    message.type === 'user' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-green-500 text-white'
                  }`}>
                    {message.type === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                  </div>
                  
                  <div className={`flex-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
                    <div className={`inline-block p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600'
                    }`}>
                      <p className="text-sm">{message.content}</p>
                      
                      {/* زر عرض التفاصيل للرسائل التي تحتوي على بيانات */}
                      {message.type === 'assistant' && message.data && message.data.length > 0 && (
                        <button
                          onClick={() => showResultDetails(message)}
                          className="mt-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-lg flex items-center space-x-1"
                        >
                          <Eye className="w-3 h-3" />
                          <span>عرض التفاصيل</span>
                        </button>
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {new Date(message.timestamp).toLocaleTimeString('ar-SA')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-end">
              <div className="flex items-center space-x-2 bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">جاري المعالجة...</span>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <form onSubmit={handleSubmit} className="flex space-x-2">
            <button
              type="submit"
              disabled={!input.trim() || isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </button>
            
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="اكتب استعلامك هنا..."
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              disabled={isLoading}
            />
          </form>
        </div>
      </div>

      {/* Result Modal */}
      {selectedResult && (
        <QueryResultModal
          isOpen={showResultModal}
          onClose={() => setShowResultModal(false)}
          result={selectedResult}
        />
      )}
    </div>
  );
}
