'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { BarChart3, <PERSON><PERSON><PERSON> as <PERSON>Chart<PERSON>con, <PERSON><PERSON>hart as Pie<PERSON><PERSON>I<PERSON>, TrendingUp } from 'lucide-react';

interface EnhancedChartProps {
  data: any[];
  title?: string;
}

type ChartType = 'bar' | 'line' | 'pie' | 'area';

export default function EnhancedChart({ data, title }: EnhancedChartProps) {
  const [chartType, setChartType] = useState<ChartType>('bar');

  // دالة لترجمة أسماء الأعمدة إلى العربية
  const translateColumnName = (columnName: string): string => {
    const translations: { [key: string]: string } = {
      // أسماء الأعمدة الأساسية
      'ItemName': 'اسم المنتج',
      'ClientName': 'اسم العميل',
      'BranchName': 'اسم الفرع',
      'CategoryName': 'اسم الفئة',
      'DocumentName': 'نوع الوثيقة',
      'TheDate': 'التاريخ',
      'Quantity': 'الكمية',
      'Amount': 'المبلغ',
      'UnitPrice': 'سعر الوحدة',
      
      // أسماء الأعمدة المحسوبة
      'TotalQuantity': 'إجمالي الكمية',
      'TotalSpent': 'إجمالي المبلغ المنفق',
      'TotalSales': 'إجمالي المبيعات',
      'TotalAmount': 'إجمالي المبلغ',
      'OrderCount': 'عدد الطلبات',
      'SalesDate': 'تاريخ المبيعات',
      'AveragePrice': 'متوسط السعر',
      'MaxPrice': 'أعلى سعر',
      'MinPrice': 'أقل سعر',
      'ProductCount': 'عدد المنتجات',
      'CustomerCount': 'عدد العملاء',
      
      // أعمدة إضافية
      'ID': 'المعرف',
      'ItemID': 'معرف المنتج',
      'ClientID': 'معرف العميل',
      'BranchID': 'معرف الفرع',
      'SerialNumber': 'الرقم التسلسلي',
      'Barcode': 'الباركود'
    };
    
    return translations[columnName] || columnName;
  };

  if (!data || data.length === 0) {
    return <div className="text-center py-8 text-gray-500">لا توجد بيانات للعرض</div>;
  }

  const columns = Object.keys(data[0]);
  const numericColumns = columns.filter(col => 
    data.some(row => typeof row[col] === 'number')
  );
  const textColumns = columns.filter(col => 
    !numericColumns.includes(col) && data.some(row => typeof row[col] === 'string')
  );

  if (numericColumns.length === 0) {
    return <div className="text-center py-8 text-gray-500">لا توجد بيانات رقمية للرسم البياني</div>;
  }

  // اختيار أفضل عمود للمحور X (نصي)
  const getBestXColumn = () => {
    const priorities = ['ItemName', 'ClientName', 'BranchName', 'CategoryName'];
    for (const priority of priorities) {
      if (textColumns.includes(priority)) return priority;
    }
    return textColumns[0] || columns[0];
  };

  // اختيار أفضل أعمدة للمحور Y (رقمية)
  const getBestYColumns = () => {
    const priorities = ['TotalQuantity', 'TotalSpent', 'TotalSales', 'Amount', 'Quantity', 'OrderCount'];
    const result = [];
    for (const priority of priorities) {
      if (numericColumns.includes(priority)) result.push(priority);
    }
    numericColumns.forEach(col => {
      if (!result.includes(col)) result.push(col);
    });
    return result.slice(0, 3);
  };

  const xColumn = getBestXColumn();
  const yColumns = getBestYColumns();

  // ترجمة البيانات
  const translatedData = data.slice(0, 20).map(row => {
    const translatedRow: any = {};
    for (const [key, value] of Object.entries(row)) {
      const arabicKey = translateColumnName(key);
      translatedRow[arabicKey] = value;
    }
    return translatedRow;
  });

  const translatedXColumn = translateColumnName(xColumn);
  const translatedYColumns = yColumns.map(col => translateColumnName(col));

  // ألوان متنوعة وجميلة
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', 
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ];

  const renderChart = () => {
    const commonProps = {
      data: translatedData,
      margin: { top: 20, right: 30, left: 20, bottom: 60 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey={translatedXColumn} 
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={12}
              stroke="#6B7280"
            />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#F9FAFB',
                border: '1px solid #E5E7EB',
                borderRadius: '8px'
              }}
            />
            <Legend />
            {translatedYColumns.map((col, index) => (
              <Line 
                key={col}
                type="monotone"
                dataKey={col} 
                stroke={colors[index % colors.length]}
                strokeWidth={3}
                dot={{ fill: colors[index % colors.length], strokeWidth: 2, r: 4 }}
                name={col}
              />
            ))}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey={translatedXColumn} 
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={12}
              stroke="#6B7280"
            />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#F9FAFB',
                border: '1px solid #E5E7EB',
                borderRadius: '8px'
              }}
            />
            <Legend />
            {translatedYColumns.map((col, index) => (
              <Area 
                key={col}
                type="monotone"
                dataKey={col} 
                stackId="1"
                stroke={colors[index % colors.length]}
                fill={colors[index % colors.length]}
                fillOpacity={0.6}
                name={col}
              />
            ))}
          </AreaChart>
        );

      case 'pie':
        // للرسم الدائري، نستخدم أول عمود رقمي فقط
        const pieData = translatedData.map((item, index) => ({
          name: item[translatedXColumn],
          value: item[translatedYColumns[0]],
          fill: colors[index % colors.length]
        }));

        return (
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
              outerRadius={120}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        );

      default: // bar
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey={translatedXColumn} 
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={12}
              stroke="#6B7280"
            />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#F9FAFB',
                border: '1px solid #E5E7EB',
                borderRadius: '8px'
              }}
            />
            <Legend />
            {translatedYColumns.map((col, index) => (
              <Bar 
                key={col}
                dataKey={col} 
                fill={colors[index % colors.length]}
                radius={[4, 4, 0, 0]}
                name={col}
              />
            ))}
          </BarChart>
        );
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {title || 'الرسم البياني'}
        </h3>
        
        {/* Chart Type Selector */}
        <div className="flex space-x-2">
          <button
            onClick={() => setChartType('bar')}
            className={`p-2 rounded-lg ${
              chartType === 'bar' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' 
                : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
            }`}
            title="رسم بياني بالأعمدة"
          >
            <BarChart3 className="w-5 h-5" />
          </button>
          <button
            onClick={() => setChartType('line')}
            className={`p-2 rounded-lg ${
              chartType === 'line' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' 
                : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
            }`}
            title="رسم بياني خطي"
          >
            <LineChartIcon className="w-5 h-5" />
          </button>
          <button
            onClick={() => setChartType('area')}
            className={`p-2 rounded-lg ${
              chartType === 'area' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' 
                : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
            }`}
            title="رسم بياني مساحي"
          >
            <TrendingUp className="w-5 h-5" />
          </button>
          <button
            onClick={() => setChartType('pie')}
            className={`p-2 rounded-lg ${
              chartType === 'pie' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' 
                : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
            }`}
            title="رسم بياني دائري"
          >
            <PieChartIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-96 w-full">
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </div>

      {/* Info */}
      <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
        عرض أول {Math.min(20, data.length)} عنصر من أصل {data.length} • 
        المحور الأفقي: {translatedXColumn} • 
        البيانات: {translatedYColumns.join(', ')}
      </div>
    </div>
  );
}
